import 'package:cal/common/extentions/colors_extension.dart';
import 'package:cal/common/widgets/app_text.dart';
import 'package:cal/features/quick_actions/exercise/presentation/pages/describe_exercise_screen.dart';
import 'package:cal/features/quick_actions/exercise/presentation/pages/manual_exercise_screen.dart';
import 'package:cal/features/quick_actions/exercise/presentation/pages/run_exercise_screen.dart';
import 'package:cal/features/quick_actions/exercise/presentation/pages/weight_lifting_exercise_screen.dart';
import 'package:flutter/material.dart';

class LogExerciseScreen extends StatelessWidget {
  const LogExerciseScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: AppText.titleLarge(
          'Log Exercise',
          color: context.onSecondary,
        ),
        centerTitle: true,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            Navigator.of(context).pop();
          },
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            AppText.titleLarge(
              'سجل تمرين',
              color: context.onSecondary,
              fontWeight: FontWeight.bold,
            ),
            const SizedBox(height: 20),
            _buildExerciseOption(
              context,
              'الجري',
              'الجري، الركض، العدو، وغيرها',
              Icons.directions_run,
              () {
                Navigator.of(context).push(MaterialPageRoute(builder: (_) => const RunExerciseScreen()));
              },
            ),
            const SizedBox(height: 16),
            _buildExerciseOption(
              context,
              'رفع الأثقال',
              'أجهزة تمارين، أوزان حرة، وغيرها',
              Icons.fitness_center,
              () {
                Navigator.of(context).push(MaterialPageRoute(builder: (_) => const WeightLiftingExerciseScreen()));
              },
            ),
            const SizedBox(height: 16),
            _buildExerciseOption(
              context,
              'وصف التمرين',
              'اكتب تمرينك بالنص',
              Icons.edit,
              () {
                Navigator.of(context).push(MaterialPageRoute(builder: (_) => const DescribeExerciseScreen()));
              },
            ),
            const SizedBox(height: 16),
            _buildExerciseOption(
              context,
              'إدخال يدوي',
              'أدخل عدد السعرات التي حرفتها يدويا',
              Icons.handyman,
              () {
                Navigator.of(context).push(MaterialPageRoute(builder: (_) => const ManualExerciseScreen()));
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildExerciseOption(
    BuildContext context,
    String title,
    String subtitle,
    IconData icon,
    VoidCallback onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16.0),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12.0),
          border: Border.all(color: context.primaryColor, width: 2.0),
        ),
        child: Row(
          children: [
            Icon(icon, color: context.primaryColor, size: 30),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  AppText.titleMedium(
                    title,
                    color: context.onSecondary,
                    fontWeight: FontWeight.bold,
                  ),
                  AppText.bodySmall(
                    subtitle,
                    color: context.onSecondary,
                  ),
                ],
              ),
            ),
            Icon(Icons.arrow_forward_ios, color: context.primaryColor, size: 20),
          ],
        ),
      ),
    );
  }
}


